{"Blog": "Blog", "Unix Timestamp Converter": "Unix Zeitstempel Konverter", "Current Unix Timestamp": "Aktueller Unix Zeitstempel", "s ⇌ ms": "s ⇌ ms", "Copy": "<PERSON><PERSON><PERSON>", "Stop": "Stoppen", "Start": "Starten", "Timestamp": "Zeitstempel", "Timestamp to Date": "Zeitstempel zu Datum", "Enter timestamp": "Zeitstempel eingeben", "Seconds": "Sekunden", "Milliseconds": "Millisekunden", "Convert": "Konvertieren", "Browser Default": "Browser Standard", "format": "Format", "Select timezone": "Zeitzone auswählen", "Unit": "Einheit", "Timezone": "Zeitzone", "convert result": "Konvertierungsergebnis", "Date to Timestamp": "<PERSON><PERSON> zu Zeitstempel", "YYYY-MM-DD hh:mm:ss": "YYYY-MM-DD hh:mm:ss", "Date": "Datum", "Discord Timestamp Converter": "Discord Zeitstempel Konverter", "Select Date and time": "Datum und Uhrzeit auswählen", "Timestamp Formats": "Zeitstempel Formate", "Unix Timestamp": "Unix Zeitstempel", "Short Time": "<PERSON><PERSON><PERSON>", "Long Time": "<PERSON>", "Short Date": "<PERSON><PERSON><PERSON>", "Long Date": "<PERSON><PERSON>", "Short Date/Time": "Kurzes <PERSON>tum/Zeit", "Long Date/Time": "Langes Datum/Zeit", "RelativeTime": "Relative Zeit", "Language": "<PERSON><PERSON><PERSON>", "Code": "Code", "How to Get Currnet Timestamp in ...": "Wie man den aktuellen Zeitstempel in ... erhält", "Discord Timestamp": "Discord Zeitstempel", "Home": "Startseite", "No blog posts found": "<PERSON><PERSON>-Beiträge gefunden", "Discord Timestamp Generator": "Discord Zeitstempel Generator", "What is a Discord Timestamp and Why is it Essential?": "Was ist ein Discord Zeitstempel und warum ist er wichtig?", "What Is a Discord Timestamp?": "Was ist ein Discord Zeitstempel?", "A Discord timestamp is a special code that automatically displays the correct time for each user based on their local time zone. Instead of manually calculating time differences or confusing your community with multiple time formats, Discord timestamps ensure everyone sees the same event time in their own local format.": "Ein Discord Zeitstempel ist ein spezieller Code, der automatisch die korrekte Zeit für jeden Benutzer basierend auf seiner lokalen Zeitzone anzeigt. Anstatt Zeitunterschiede manuell zu berechnen oder Ihre Community mit mehreren Zeitformaten zu verwirren, stellen Discord Zeitstempel sicher, dass jeder die gleiche Ereigniszeit in seinem eigenen lokalen Format sieht.", "Why Are Discord Timestamps Essential for Community Management?": "Warum sind Discord Zeitstempel für das Community Management wichtig?", "Managing a global Discord community means dealing with members across different time zones. Without Discord timestamps, scheduling events becomes a nightmare of manual time zone conversions and constant clarifications.": "Die Verwaltung einer globalen Discord Community bedeutet, mit Mitgliedern aus verschiedenen Zeitzonen umzugehen. Ohne Discord Zeitstempel wird die Terminplanung zu einem Albtraum aus manuellen Zeitzonenumrechnungen und ständigen Klarstellungen.", "An incorrect event time can cause members to miss critical content. By using a Discord timestamp, you can eliminate problems caused by time-related misunderstandings right from the source. When everyone sees one, unified, and correct time, you as the organizer no longer need to spend extra energy on repeated explanations and confirmations.": "Eine falsche Ereigniszeit kann dazu führen, dass Mitglieder wichtige Inhalte verpassen. Durch die Verwendung eines Discord Zeitstempels können Sie Probleme, die durch zeitbezogene Missverständnisse verursacht werden, direkt an der Quelle beseitigen. Wenn jeder eine einheitliche und korrekte Zeit sieht, müssen Sie als Organisator keine zusätzliche Energie für wiederholte Erklärungen und Bestätigungen aufwenden.", "How to Use Our Discord Timestamp Generator": "Wie man unseren Discord Zeitstempel Generator verwendet", "With our Discord timestamp generator, you don't need to know anything about complex Unix time. Just follow these simple steps to create the perfect Discord timestamp in seconds.": "Mit unserem Discord Zeitstempel Generator müssen Sie nichts über komplexe Unix Zeit wissen. Folgen Sie einfach diesen einfachen Schritten, um in Sekunden den perfekten Discord Zeitstempel zu erstellen.", "Step 1: Select Your Date and Time": "Schritt 1: <PERSON><PERSON><PERSON><PERSON> Sie Ihr Datum und Ihre Uhrzeit", "Use our intuitive date and time picker to choose when your event will occur. The interface is designed to be user-friendly, allowing you to quickly navigate to any date and set the exact time for your Discord timestamp.": "Verwenden Sie unseren intuitiven Datum- und Zeitwähler, um zu wählen, wann Ihr Ereignis stattfinden wird. Die Benutzeroberfläche ist benutzerfreundlich gestaltet und ermöglicht es Ihnen, schnell zu jedem Datum zu navigieren und die genaue Zeit für Ihren Discord Zeitstempel festzulegen.", "Step 2: Choose Your Discord Timestamp Format": "Schritt 2: <PERSON><PERSON><PERSON><PERSON> Sie Ihr Discord Zeitstempel Format", "Select from seven different Discord timestamp formats. Each format displays time differently - from short time formats to relative time that shows 'in 3 hours' or '2 days ago'. Preview each format to see exactly how your Discord timestamp will appear to users.": "<PERSON><PERSON><PERSON>en Sie aus sieben verschiedenen Discord Zeitstempel Formaten. Jedes Format zeigt die Zeit anders an - von kurzen Zeitformaten bis hin zu relativer Zeit, die 'in 3 Stunden' oder 'vor 2 Tagen' anzeigt. <PERSON><PERSON> sich jedes Format in der Vorschau an, um genau zu sehen, wie Ihr Discord Zeitstempel den Benutzern erscheinen wird.", "Step 3: Copy Your Discord Timestamp Code": "Schritt 3: <PERSON><PERSON><PERSON> Ihren Discord Zeitstempel Code", "Click the copy button next to your preferred format. This copies the complete Discord timestamp code (like `<t:1786323810:R>`) to your clipboard, ready to paste into any Discord message.": "Klicken Sie auf die Kopieren-Schaltfläche neben Ihrem bevorzugten Format. Dies kopiert den vollständigen Discord Zeitstempel Code (wie `<t:1786323810:R>`) in Ihre Zwischenablage, bereit zum Einfügen in jede Discord Nachricht.", "Step 4: Paste and Send": "Schritt 4: <PERSON><PERSON><PERSON><PERSON> und Senden", "Return to your Discord client and paste the copied code into a chat box, event announcement, or anywhere you want the time to appear. Please note, it will look like code in your message box before you hit send. Once the message is sent, that Discord timestamp will magically transform into a clear, localized time for everyone to see!": "Kehren Sie zu Ihrem Discord Client zurück und fügen Sie den kopierten Code in ein Chatfeld, eine Ereignisankündigung oder überall dort ein, wo die Zeit erscheinen soll. Bitte beachten Sie, dass es in Ihrem Nachrichtenfeld wie Code aussehen wird, bevor <PERSON> senden drücken. <PERSON><PERSON><PERSON> die Nachricht gesendet wird, verwan<PERSON><PERSON> sich dieser Discord Zeitstempel magisch in eine klare, lokalisierte Zeit, die jeder sehen kann!", "Discord Timestamp Formats": "Discord Zeitstempel Formate", "Discord uses the <t:timestamp:format> syntax, supporting seven formats:": "Discord verwendet die <t:timestamp:format> Syntax und unterstützt sieben Formate:", "t: Short time (e.g., 4:20 PM)": "t: <PERSON><PERSON><PERSON> (z.B. 16:20)", "T: Long time (e.g., 4:20:30 PM)": "T: <PERSON> (z.B. 16:20:30)", "d: Short date (e.g., 04/20/2024)": "d: <PERSON><PERSON><PERSON> (z.B. 20.04.2024)", "D: Long date (e.g., April 20, 2024)": "D: <PERSON><PERSON> (z.B. 20. April 2024)", "f: Short date/time (e.g., April 20, 2024 4:20 PM)": "f: <PERSON><PERSON><PERSON>/Zeit (z.B. 20. April 2024 16:20)", "F: Long date/time (e.g., Saturday, April 20, 2024 4:20 PM)": "F: <PERSON><PERSON>/Zeit (z.B. Samstag, 20. April 2024 16:20)", "R: Relative time (e.g., 2 months ago, 3 days from now)": "R: <PERSON><PERSON> (z.B. vor 2 Monaten, in 3 Tagen)", "Key Features of Our Discord Timestamp Generator": "Hauptfunktionen unseres Discord Zeitstempel Generators", "Intuitive Date & Time Picker": "Intuitiver Datum- und Zeitwähler", "Forget about manually handling Unix time. Our user-friendly interface lets you visually select any date and time to create your perfect Discord timestamp instantly.": "Vergessen Sie die manuelle Handhabung von Unix Zeit. Unsere benutzerfreundliche Oberfläche ermöglicht es Ihnen, visuell jedes <PERSON> und jede Z<PERSON> auszuwählen, um sofort Ihren perfekten Discord Zeitstempel zu erstellen.", "Complete Format Support": "Vollständige Format-Unterstützung", "We support all seven native styles, giving you full control over how your Discord timestamp appears. Find the ideal format for any event, announcement, or message.": "Wir unterstützen alle sieben nativen Stile und geben Ihnen die volle Kontrolle darüber, wie Ihr Discord Zeitstempel erscheint. Finden Sie das ideale Format für jedes Ereignis, jede Ankündigung oder Nachricht.", "Live Preview of Your Timestamp": "Live-Vorschau Ihres Zeitstempels", "Our generator shows you exactly how your Discord timestamp will look in Discord before you copy it. This eliminates guesswork and ensures you always get the perfect result.": "Unser Generator zeigt <PERSON> g<PERSON>, wie Ihr Discord Zeitstempel in Discord aussehen wird, bevor <PERSON> ihn kopieren. Dies eliminiert <PERSON> und stellt sicher, dass Sie immer das perfekte Ergebnis erhalten.", "Instant Copy & Paste": "Sofortiges Kopieren & Einfügen", "One click copies your Discord timestamp code to the clipboard. No manual typing, no errors - just paste directly into Discord and watch the magic happen.": "Ein Klick kopiert Ihren Discord Zeitstempel Code in die Zwischenablage. <PERSON>ine manuelle Eingabe, keine <PERSON> - fügen Si<PERSON> einfach direkt in Discord ein und sehen <PERSON>, wie die Magie geschieht.", "Cross-Platform Compatibility": "Plattformübergreifende Kompatibilität", "Your Discord timestamp will work perfectly across all Discord platforms - desktop, web, and mobile apps. Create once, use everywhere.": "Ihr Discord Zeitstempel funktioniert perfekt auf allen Discord Plattformen - Desktop, Web und mobile Apps. Einmal erstellen, überall verwenden.", "Free Forever": "<PERSON><PERSON><PERSON> immer kosten<PERSON>", "Our Discord timestamp generator is completely free with no hidden costs, registration requirements, or usage limits. Create unlimited Discord timestamps whenever you need them.": "Unser Discord Zeitstempel Generator ist völlig kostenlos ohne versteckte Kosten, Registrierungsanforderungen oder Nutzungsbeschränkungen. Erstellen Sie unbegrenzt Discord Zeitstempel, wann immer Si<PERSON> sie benötigen."}