{"Blog": "ブログ", "Unix Timestamp Converter": "Unix タイムスタンプ変換器", "Current Unix Timestamp": "現在の Unix タイムスタンプ", "s ⇌ ms": "秒 ⇌ ミリ秒", "Copy": "コピー", "Stop": "停止", "Start": "開始", "Timestamp": "タイムスタンプ", "Timestamp to Date": "タイムスタンプから日付へ", "Enter timestamp": "タイムスタンプを入力", "Seconds": "秒", "Milliseconds": "ミリ秒", "Convert": "変換", "Browser Default": "ブラウザのデフォルト", "format": "形式", "Select timezone": "タイムゾーンを選択", "Unit": "単位", "Timezone": "タイムゾーン", "convert result": "変換結果", "Date to Timestamp": "日付からタイムスタンプへ", "YYYY-MM-DD hh:mm:ss": "YYYY-MM-DD hh:mm:ss", "Date": "日付", "Discord Timestamp Converter": "Discord タイムスタンプ変換器", "Select Date and time": "日付と時刻を選択", "Timestamp Formats": "タイムスタンプ形式", "Unix Timestamp": "Unix タイムスタンプ", "Short Time": "短い時刻", "Long Time": "長い時刻", "Short Date": "短い日付", "Long Date": "長い日付", "Short Date/Time": "短い日付/時刻", "Long Date/Time": "長い日付/時刻", "RelativeTime": "相対時間", "Language": "言語", "Code": "コード", "How to Get Currnet Timestamp in ...": "...で現在のタイムスタンプを取得する方法", "Discord Timestamp": "Discord タイムスタンプ", "Home": "ホーム", "No blog posts found": "ブログ記事が見つかりません", "Discord Timestamp Generator": "Discord タイムスタンプジェネレーター", "What is a Discord Timestamp and Why is it Essential?": "Discord タイムスタンプとは何か、なぜ重要なのか？", "What Is a Discord Timestamp?": "Discord タイムスタンプとは？", "A Discord timestamp is a special code that automatically displays the correct time for each user based on their local time zone. Instead of manually calculating time differences or confusing your community with multiple time formats, Discord timestamps ensure everyone sees the same event time in their own local format.": "Discord タイムスタンプは、各ユーザーの現地時間帯に基づいて自動的に正しい時刻を表示する特別なコードです。時差を手動で計算したり、複数の時刻形式でコミュニティを混乱させる代わりに、Discord タイムスタンプは全員が同じイベント時刻を自分の現地形式で見ることを保証します。", "Why Are Discord Timestamps Essential for Community Management?": "なぜ Discord タイムスタンプはコミュニティ管理に不可欠なのか？", "Managing a global Discord community means dealing with members across different time zones. Without Discord timestamps, scheduling events becomes a nightmare of manual time zone conversions and constant clarifications.": "グローバルな Discord コミュニティを管理するということは、異なるタイムゾーンのメンバーを扱うことを意味します。Discord タイムスタンプがなければ、イベントのスケジューリングは手動のタイムゾーン変換と絶え間ない説明の悪夢となります。", "An incorrect event time can cause members to miss critical content. By using a Discord timestamp, you can eliminate problems caused by time-related misunderstandings right from the source. When everyone sees one, unified, and correct time, you as the organizer no longer need to spend extra energy on repeated explanations and confirmations.": "間違ったイベント時刻は、メンバーが重要なコンテンツを見逃す原因となります。Discord タイムスタンプを使用することで、時間に関連する誤解によって引き起こされる問題を根本から排除できます。全員が統一された正しい時刻を見ることで、主催者であるあなたは繰り返しの説明や確認に余分なエネルギーを費やす必要がなくなります。", "How to Use Our Discord Timestamp Generator": "Discord タイムスタンプジェネレーターの使用方法", "With our Discord timestamp generator, you don't need to know anything about complex Unix time. Just follow these simple steps to create the perfect Discord timestamp in seconds.": "私たちの Discord タイムスタンプジェネレーターを使用すれば、複雑な Unix 時間について何も知る必要はありません。これらの簡単な手順に従うだけで、数秒で完璧な Discord タイムスタンプを作成できます。", "Step 1: Select Your Date and Time": "ステップ 1：日付と時刻を選択", "Use our intuitive date and time picker to choose when your event will occur. The interface is designed to be user-friendly, allowing you to quickly navigate to any date and set the exact time for your Discord timestamp.": "直感的な日付と時刻ピッカーを使用して、イベントがいつ発生するかを選択してください。インターフェースはユーザーフレンドリーに設計されており、任意の日付に素早くナビゲートし、Discord タイムスタンプの正確な時刻を設定できます。", "Step 2: Choose Your Discord Timestamp Format": "ステップ 2：Discord タイムスタンプ形式を選択", "Select from seven different Discord timestamp formats. Each format displays time differently - from short time formats to relative time that shows 'in 3 hours' or '2 days ago'. Preview each format to see exactly how your Discord timestamp will appear to users.": "7つの異なる Discord タイムスタンプ形式から選択してください。各形式は時刻を異なって表示します - 短い時刻形式から「3時間後」や「2日前」を表示する相対時間まで。各形式をプレビューして、Discord タイムスタンプがユーザーにどのように表示されるかを正確に確認してください。", "Step 3: Copy Your Discord Timestamp Code": "ステップ 3：Discord タイムスタンプコードをコピー", "Click the copy button next to your preferred format. This copies the complete Discord timestamp code (like `<t:1786323810:R>`) to your clipboard, ready to paste into any Discord message.": "お好みの形式の横にあるコピーボタンをクリックしてください。これにより、完全な Discord タイムスタンプコード（`<t:1786323810:R>` のような）がクリップボードにコピーされ、任意の Discord メッセージに貼り付ける準備が整います。", "Step 4: Paste and Send": "ステップ 4：貼り付けて送信", "Return to your Discord client and paste the copied code into a chat box, event announcement, or anywhere you want the time to appear. Please note, it will look like code in your message box before you hit send. Once the message is sent, that Discord timestamp will magically transform into a clear, localized time for everyone to see!": "Discord クライアントに戻り、コピーしたコードをチャットボックス、イベント告知、または時刻を表示したい任意の場所に貼り付けてください。送信ボタンを押す前は、メッセージボックスでコードのように見えることに注意してください。メッセージが送信されると、その Discord タイムスタンプは魔法のように明確でローカライズされた時刻に変換され、全員が見ることができます！", "Discord Timestamp Formats": "Discord タイムスタンプ形式", "Discord uses the <t:timestamp:format> syntax, supporting seven formats:": "Discord は <t:timestamp:format> 構文を使用し、7つの形式をサポートしています：", "t: Short time (e.g., 4:20 PM)": "t: 短い時刻（例：午後4:20）", "T: Long time (e.g., 4:20:30 PM)": "T: 長い時刻（例：午後4:20:30）", "d: Short date (e.g., 04/20/2024)": "d: 短い日付（例：2024/04/20）", "D: Long date (e.g., April 20, 2024)": "D: 長い日付（例：2024年4月20日）", "f: Short date/time (e.g., April 20, 2024 4:20 PM)": "f: 短い日付/時刻（例：2024年4月20日 午後4:20）", "F: Long date/time (e.g., Saturday, April 20, 2024 4:20 PM)": "F: 長い日付/時刻（例：2024年4月20日土曜日 午後4:20）", "R: Relative time (e.g., 2 months ago, 3 days from now)": "R: 相対時間（例：2ヶ月前、3日後）", "Key Features of Our Discord Timestamp Generator": "Discord タイムスタンプジェネレーターの主要機能", "Intuitive Date & Time Picker": "直感的な日付・時刻ピッカー", "Forget about manually handling Unix time. Our user-friendly interface lets you visually select any date and time to create your perfect Discord timestamp instantly.": "Unix 時間の手動処理は忘れてください。ユーザーフレンドリーなインターフェースにより、任意の日付と時刻を視覚的に選択して、完璧な Discord タイムスタンプを瞬時に作成できます。", "Complete Format Support": "完全な形式サポート", "We support all seven native styles, giving you full control over how your Discord timestamp appears. Find the ideal format for any event, announcement, or message.": "7つのネイティブスタイルすべてをサポートし、Discord タイムスタンプの表示方法を完全にコントロールできます。任意のイベント、告知、またはメッセージに理想的な形式を見つけてください。", "Live Preview of Your Timestamp": "タイムスタンプのライブプレビュー", "Our generator shows you exactly how your Discord timestamp will look in Discord before you copy it. This eliminates guesswork and ensures you always get the perfect result.": "ジェネレーターは、コピーする前に Discord タイムスタンプが Discord でどのように見えるかを正確に表示します。これにより推測が不要になり、常に完璧な結果を得ることができます。", "Instant Copy & Paste": "瞬時コピー＆ペースト", "One click copies your Discord timestamp code to the clipboard. No manual typing, no errors - just paste directly into Discord and watch the magic happen.": "ワンクリックで Discord タイムスタンプコードをクリップボードにコピーします。手動入力なし、エラーなし - Discord に直接貼り付けて魔法が起こるのを見てください。", "Cross-Platform Compatibility": "クロスプラットフォーム互換性", "Your Discord timestamp will work perfectly across all Discord platforms - desktop, web, and mobile apps. Create once, use everywhere.": "Discord タイムスタンプは、すべての Discord プラットフォーム（デスクトップ、ウェブ、モバイルアプリ）で完璧に動作します。一度作成すれば、どこでも使用できます。", "Free Forever": "永久無料", "Our Discord timestamp generator is completely free with no hidden costs, registration requirements, or usage limits. Create unlimited Discord timestamps whenever you need them.": "Discord タイムスタンプジェネレーターは完全に無料で、隠れたコスト、登録要件、使用制限はありません。必要な時にいつでも無制限の Discord タイムスタンプを作成できます。"}