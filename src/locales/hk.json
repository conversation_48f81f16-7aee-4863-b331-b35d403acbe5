{"Blog": "網誌", "Unix Timestamp Converter": "Unix 時間戳轉換器", "Current Unix Timestamp": "目前 Unix 時間戳", "s ⇌ ms": "秒 ⇌ 毫秒", "Copy": "複製", "Stop": "停止", "Start": "開始", "Timestamp": "時間戳", "Timestamp to Date": "時間戳轉日期", "Enter timestamp": "輸入時間戳", "Seconds": "秒", "Milliseconds": "毫秒", "Convert": "轉換", "Browser Default": "瀏覽器預設", "format": "格式", "Select timezone": "選擇時區", "Unit": "單位", "Timezone": "時區", "convert result": "轉換結果", "Date to Timestamp": "日期轉時間戳", "YYYY-MM-DD hh:mm:ss": "YYYY-MM-DD hh:mm:ss", "Date": "日期", "Discord Timestamp Converter": "Discord 時間戳轉換器", "Select Date and time": "選擇日期和時間", "Timestamp Formats": "時間戳格式", "Unix Timestamp": "Unix 時間戳", "Short Time": "短時間", "Long Time": "長時間", "Short Date": "短日期", "Long Date": "長日期", "Short Date/Time": "短日期/時間", "Long Date/Time": "長日期/時間", "RelativeTime": "相對時間", "Language": "語言", "Code": "程式碼", "How to Get Currnet Timestamp in ...": "如何在...中取得目前時間戳", "Discord Timestamp": "Discord 時間戳", "Home": "首頁", "No blog posts found": "找不到網誌文章", "Discord Timestamp Generator": "Discord 時間戳產生器", "What is a Discord Timestamp and Why is it Essential?": "什麼是 Discord 時間戳，為什麼它如此重要？", "What Is a Discord Timestamp?": "什麼是 Discord 時間戳？", "A Discord timestamp is a special code that automatically displays the correct time for each user based on their local time zone. Instead of manually calculating time differences or confusing your community with multiple time formats, Discord timestamps ensure everyone sees the same event time in their own local format.": "Discord 時間戳是一種特殊程式碼，它會根據每個使用者的本地時區自動顯示正確的時間。無需手動計算時差或用多種時間格式讓社群成員感到困惑，Discord 時間戳確保每個人都能以自己的本地格式看到相同的活動時間。", "Why Are Discord Timestamps Essential for Community Management?": "為什麼 Discord 時間戳對社群管理至關重要？", "Managing a global Discord community means dealing with members across different time zones. Without Discord timestamps, scheduling events becomes a nightmare of manual time zone conversions and constant clarifications.": "管理全球 Discord 社群意味著要處理來自不同時區的成員。沒有 Discord 時間戳，安排活動就會變成手動時區轉換和不斷澄清的惡夢。", "An incorrect event time can cause members to miss critical content. By using a Discord timestamp, you can eliminate problems caused by time-related misunderstandings right from the source. When everyone sees one, unified, and correct time, you as the organizer no longer need to spend extra energy on repeated explanations and confirmations.": "錯誤的活動時間可能導致成員錯過重要內容。透過使用 Discord 時間戳，您可以從源頭消除因時間相關誤解而引起的問題。當每個人都看到統一且正確的時間時，作為組織者的您就不再需要在重複解釋和確認上花費額外精力。", "How to Use Our Discord Timestamp Generator": "如何使用我們的 Discord 時間戳產生器", "With our Discord timestamp generator, you don't need to know anything about complex Unix time. Just follow these simple steps to create the perfect Discord timestamp in seconds.": "使用我們的 Discord 時間戳產生器，您無需了解複雜的 Unix 時間。只需按照這些簡單步驟，即可在幾秒鐘內建立完美的 Discord 時間戳。", "Step 1: Select Your Date and Time": "步驟 1：選擇您的日期和時間", "Use our intuitive date and time picker to choose when your event will occur. The interface is designed to be user-friendly, allowing you to quickly navigate to any date and set the exact time for your Discord timestamp.": "使用我們直觀的日期和時間選擇器來選擇您的活動何時舉行。介面設計使用者友善，讓您可以快速導航到任何日期並為您的 Discord 時間戳設定確切時間。", "Step 2: Choose Your Discord Timestamp Format": "步驟 2：選擇您的 Discord 時間戳格式", "Select from seven different Discord timestamp formats. Each format displays time differently - from short time formats to relative time that shows 'in 3 hours' or '2 days ago'. Preview each format to see exactly how your Discord timestamp will appear to users.": "從七種不同的 Discord 時間戳格式中選擇。每種格式顯示時間的方式不同 - 從短時間格式到顯示「3小時後」或「2天前」的相對時間。預覽每種格式，準確了解您的 Discord 時間戳將如何向使用者顯示。", "Step 3: Copy Your Discord Timestamp Code": "步驟 3：複製您的 Discord 時間戳程式碼", "Click the copy button next to your preferred format. This copies the complete Discord timestamp code (like `<t:1786323810:R>`) to your clipboard, ready to paste into any Discord message.": "點擊您首選格式旁邊的複製按鈕。這會將完整的 Discord 時間戳程式碼（如 `<t:1786323810:R>`）複製到您的剪貼簿，準備貼上到任何 Discord 訊息中。", "Step 4: Paste and Send": "步驟 4：貼上並傳送", "Return to your Discord client and paste the copied code into a chat box, event announcement, or anywhere you want the time to appear. Please note, it will look like code in your message box before you hit send. Once the message is sent, that Discord timestamp will magically transform into a clear, localized time for everyone to see!": "返回您的 Discord 用戶端，將複製的程式碼貼上到聊天框、活動公告或您希望時間出現的任何地方。請注意，在您點擊傳送之前，它在訊息框中看起來像程式碼。一旦訊息傳送，該 Discord 時間戳將神奇地轉換為清晰的本地化時間，供所有人查看！", "Discord Timestamp Formats": "Discord 時間戳格式", "Discord uses the <t:timestamp:format> syntax, supporting seven formats:": "Discord 使用 <t:timestamp:format> 語法，支援七種格式：", "t: Short time (e.g., 4:20 PM)": "t: 短時間（例如：下午 4:20）", "T: Long time (e.g., 4:20:30 PM)": "T: 長時間（例如：下午 4:20:30）", "d: Short date (e.g., 04/20/2024)": "d: 短日期（例如：2024/04/20）", "D: Long date (e.g., April 20, 2024)": "D: 長日期（例如：2024年4月20日）", "f: Short date/time (e.g., April 20, 2024 4:20 PM)": "f: 短日期/時間（例如：2024年4月20日 下午4:20）", "F: Long date/time (e.g., Saturday, April 20, 2024 4:20 PM)": "F: 長日期/時間（例如：2024年4月20日星期六 下午4:20）", "R: Relative time (e.g., 2 months ago, 3 days from now)": "R: 相對時間（例如：2個月前，3天後）", "Key Features of Our Discord Timestamp Generator": "我們的 Discord 時間戳產生器的主要功能", "Intuitive Date & Time Picker": "直觀的日期和時間選擇器", "Forget about manually handling Unix time. Our user-friendly interface lets you visually select any date and time to create your perfect Discord timestamp instantly.": "忘記手動處理 Unix 時間。我們使用者友善的介面讓您可以直觀地選擇任何日期和時間，立即建立完美的 Discord 時間戳。", "Complete Format Support": "完整格式支援", "We support all seven native styles, giving you full control over how your Discord timestamp appears. Find the ideal format for any event, announcement, or message.": "我們支援所有七種原生樣式，讓您完全控制 Discord 時間戳的顯示方式。為任何活動、公告或訊息找到理想的格式。", "Live Preview of Your Timestamp": "時間戳即時預覽", "Our generator shows you exactly how your Discord timestamp will look in Discord before you copy it. This eliminates guesswork and ensures you always get the perfect result.": "我們的產生器在您複製之前準確顯示您的 Discord 時間戳在 Discord 中的外觀。這消除了猜測，確保您始終獲得完美的結果。", "Instant Copy & Paste": "即時複製和貼上", "One click copies your Discord timestamp code to the clipboard. No manual typing, no errors - just paste directly into Discord and watch the magic happen.": "一鍵將您的 Discord 時間戳程式碼複製到剪貼簿。無需手動輸入，無錯誤 - 只需直接貼上到 Discord 中，觀看魔法發生。", "Cross-Platform Compatibility": "跨平台相容性", "Your Discord timestamp will work perfectly across all Discord platforms - desktop, web, and mobile apps. Create once, use everywhere.": "您的 Discord 時間戳將在所有 Discord 平台上完美運行 - 桌面、網頁和行動應用程式。一次建立，隨處使用。", "Free Forever": "永久免費", "Our Discord timestamp generator is completely free with no hidden costs, registration requirements, or usage limits. Create unlimited Discord timestamps whenever you need them.": "我們的 Discord 時間戳產生器完全免費，沒有隱藏費用、註冊要求或使用限制。隨時建立無限的 Discord 時間戳。"}