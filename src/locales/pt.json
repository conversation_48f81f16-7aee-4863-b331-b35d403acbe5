{"Blog": "Blog", "Unix Timestamp Converter": "Conversor de Timestamp Unix", "Current Unix Timestamp": "Timestamp Unix Atual", "s ⇌ ms": "s ⇌ ms", "Copy": "Copiar", "Stop": "<PERSON><PERSON>", "Start": "Iniciar", "Timestamp": "Timestamp", "Timestamp to Date": "Timestamp para Data", "Enter timestamp": "Digite o timestamp", "Seconds": "<PERSON><PERSON><PERSON>", "Milliseconds": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "Convert": "Converter", "Browser Default": "Padrão do Navegador", "format": "formato", "Select timezone": "Selecionar fuso horário", "Unit": "Unidade", "Timezone": "<PERSON><PERSON>", "convert result": "resultado da conversão", "Date to Timestamp": "Data para Timestamp", "YYYY-MM-DD hh:mm:ss": "YYYY-MM-DD hh:mm:ss", "Date": "Data", "Discord Timestamp Converter": "Conversor de Timestamp do Discord", "Select Date and time": "Selecionar data e hora", "Timestamp Formats": "Formatos de Timestamp", "Unix Timestamp": "Timestamp Unix", "Short Time": "<PERSON><PERSON>", "Long Time": "<PERSON><PERSON>", "Short Date": "Data Curta", "Long Date": "<PERSON>a", "Short Date/Time": "Data/Hora Curta", "Long Date/Time": "Data/Hora <PERSON>", "RelativeTime": "Tempo Relativo", "Language": "Idioma", "Code": "Código", "How to Get Currnet Timestamp in ...": "Como Obter o Timestamp Atual em ...", "Discord Timestamp": "Timestamp do Discord", "Home": "Início", "No blog posts found": "Nenhuma postagem do blog encontrada", "Discord Timestamp Generator": "Gerador de Timestamp do Discord", "What is a Discord Timestamp and Why is it Essential?": "O que é um Timestamp do Discord e Por que é Essencial?", "What Is a Discord Timestamp?": "O que é um Timestamp do Discord?", "A Discord timestamp is a special code that automatically displays the correct time for each user based on their local time zone. Instead of manually calculating time differences or confusing your community with multiple time formats, Discord timestamps ensure everyone sees the same event time in their own local format.": "Um timestamp do Discord é um código especial que exibe automaticamente a hora correta para cada usuário com base em seu fuso horário local. Em vez de calcular manualmente as diferenças de horário ou confundir sua comunidade com múltiplos formatos de hora, os timestamps do Discord garantem que todos vejam o mesmo horário do evento em seu formato local.", "Why Are Discord Timestamps Essential for Community Management?": "Por que os Timestamps do Discord são Essenciais para o Gerenciamento de Comunidades?", "Managing a global Discord community means dealing with members across different time zones. Without Discord timestamps, scheduling events becomes a nightmare of manual time zone conversions and constant clarifications.": "Gerenciar uma comunidade global do Discord significa lidar com membros de diferentes fusos horários. Sem timestamps do Discord, agendar eventos se torna um pesadelo de conversões manuais de fuso horário e esclarecimentos constantes.", "An incorrect event time can cause members to miss critical content. By using a Discord timestamp, you can eliminate problems caused by time-related misunderstandings right from the source. When everyone sees one, unified, and correct time, you as the organizer no longer need to spend extra energy on repeated explanations and confirmations.": "Um horário de evento incorreto pode fazer com que os membros percam conteúdo crítico. Ao usar um timestamp do Discord, você pode eliminar problemas causados por mal-entendidos relacionados ao tempo diretamente da fonte. Quando todos veem um horário unificado e correto, você como organizador não precisa mais gastar energia extra em explicações e confirmações repetidas.", "How to Use Our Discord Timestamp Generator": "Como Usar Nosso Gerador de Timestamp do Discord", "With our Discord timestamp generator, you don't need to know anything about complex Unix time. Just follow these simple steps to create the perfect Discord timestamp in seconds.": "Com nosso gerador de timestamp do Discord, você não precisa saber nada sobre o tempo Unix complexo. Apenas siga estes passos simples para criar o timestamp perfeito do Discord em segundos.", "Step 1: Select Your Date and Time": "Passo 1: Selecione sua Data e Hora", "Use our intuitive date and time picker to choose when your event will occur. The interface is designed to be user-friendly, allowing you to quickly navigate to any date and set the exact time for your Discord timestamp.": "Use nosso seletor intuitivo de data e hora para escolher quando seu evento ocorrerá. A interface é projetada para ser amigável ao usuário, permitindo que você navegue rapidamente para qualquer data e defina a hora exata para seu timestamp do Discord.", "Step 2: Choose Your Discord Timestamp Format": "Passo 2: Escolha seu Formato de Timestamp do Discord", "Select from seven different Discord timestamp formats. Each format displays time differently - from short time formats to relative time that shows 'in 3 hours' or '2 days ago'. Preview each format to see exactly how your Discord timestamp will appear to users.": "Selecione entre sete formatos diferentes de timestamp do Discord. Cada formato exibe o tempo de forma diferente - de formatos de hora curtos a tempo relativo que mostra 'em 3 horas' ou '2 dias atrás'. Visualize cada formato para ver exatamente como seu timestamp do Discord aparecerá para os usuários.", "Step 3: Copy Your Discord Timestamp Code": "Passo 3: <PERSON><PERSON> seu Código de Timestamp do Discord", "Click the copy button next to your preferred format. This copies the complete Discord timestamp code (like `<t:1786323810:R>`) to your clipboard, ready to paste into any Discord message.": "Clique no botão copiar ao lado do seu formato preferido. Isso copia o código completo do timestamp do Discord (como `<t:1786323810:R>`) para sua área de transferência, pronto para colar em qualquer mensagem do Discord.", "Step 4: Paste and Send": "Passo 4: Colar e Enviar", "Return to your Discord client and paste the copied code into a chat box, event announcement, or anywhere you want the time to appear. Please note, it will look like code in your message box before you hit send. Once the message is sent, that Discord timestamp will magically transform into a clear, localized time for everyone to see!": "Retorne ao seu cliente Discord e cole o código copiado em uma caixa de chat, anún<PERSON> de evento, ou em qualquer lugar que você queira que a hora apareça. Note que parecerá código em sua caixa de mensagem antes de você pressionar enviar. Uma vez que a mensagem é enviada, esse timestamp do Discord se transformará magicamente em uma hora clara e localizada para todos verem!", "Discord Timestamp Formats": "Formatos de Timestamp do Discord", "Discord uses the <t:timestamp:format> syntax, supporting seven formats:": "O Discord usa a sintaxe <t:timestamp:format>, suportando sete formatos:", "t: Short time (e.g., 4:20 PM)": "t: <PERSON>ra curta (ex. 16:20)", "T: Long time (e.g., 4:20:30 PM)": "T: <PERSON><PERSON> longa (ex. 16:20:30)", "d: Short date (e.g., 04/20/2024)": "d: Data curta (ex. 20/04/2024)", "D: Long date (e.g., April 20, 2024)": "D: Data longa (ex. 20 de abril de 2024)", "f: Short date/time (e.g., April 20, 2024 4:20 PM)": "f: Data/hora curta (ex. 20 de abril de 2024 16:20)", "F: Long date/time (e.g., Saturday, April 20, 2024 4:20 PM)": "F: Data/hora longa (ex. sábado, 20 de abril de 2024 16:20)", "R: Relative time (e.g., 2 months ago, 3 days from now)": "R: Tempo relativo (ex. 2 meses atrás, em 3 dias)", "Key Features of Our Discord Timestamp Generator": "Características Principais do Nosso Gerador de Timestamp do Discord", "Intuitive Date & Time Picker": "Seletor Intuitivo de Data e Hora", "Forget about manually handling Unix time. Our user-friendly interface lets you visually select any date and time to create your perfect Discord timestamp instantly.": "Esqueça o manuseio manual do tempo Unix. Nossa interface amigável permite que você selecione visualmente qualquer data e hora para criar seu timestamp perfeito do Discord instantaneamente.", "Complete Format Support": "Suporte Completo de Formatos", "We support all seven native styles, giving you full control over how your Discord timestamp appears. Find the ideal format for any event, announcement, or message.": "Suportamos todos os sete estilos nativos, dando-lhe controle total sobre como seu timestamp do Discord aparece. Encontre o formato ideal para qualquer evento, an<PERSON><PERSON> ou mensagem.", "Live Preview of Your Timestamp": "Visualização ao Vivo do seu Timestamp", "Our generator shows you exactly how your Discord timestamp will look in Discord before you copy it. This eliminates guesswork and ensures you always get the perfect result.": "Nosso gerador mostra exatamente como seu timestamp do Discord ficará no Discord antes de você copiá-lo. Isso elimina suposições e garante que você sempre obtenha o resultado perfeito.", "Instant Copy & Paste": "Copiar e Colar Instantâneo", "One click copies your Discord timestamp code to the clipboard. No manual typing, no errors - just paste directly into Discord and watch the magic happen.": "Um clique copia seu código de timestamp do Discord para a área de transferência. Sem digitação manual, sem erros - apenas cole diretamente no Discord e veja a mágica acontecer.", "Cross-Platform Compatibility": "Compatibilidade Multiplataforma", "Your Discord timestamp will work perfectly across all Discord platforms - desktop, web, and mobile apps. Create once, use everywhere.": "Seu timestamp do Discord funcionará perfeitamente em todas as plataformas do Discord - desktop, web e aplicativos móveis. Crie uma vez, use em qualquer lugar.", "Free Forever": "<PERSON><PERSON><PERSON><PERSON> para Sempre", "Our Discord timestamp generator is completely free with no hidden costs, registration requirements, or usage limits. Create unlimited Discord timestamps whenever you need them.": "Nosso gerador de timestamp do Discord é completamente gratuito sem custos ocultos, requisitos de registro ou limites de uso. Crie timestamps ilimitados do Discord sempre que precisar.", "With our Unix timestamp converter, you can easily perform timestamp to date conversions (e.g., 1697059200 to \"October 12, 2023, 00:00:00 UTC\") and date to timestamp conversions (e.g., \"October 12, 2023\" to 1697059200). These features are perfect for developers working on user interfaces, debugging logs, or integrating APIs with different time formats.": "Com nosso conversor de timestamp Unix, você pode facilmente realizar conversões de timestamp para data (ex., 1697059200 para \"12 de outubro de 2023, 00:00:00 UTC\") e conversões de data para timestamp (ex., \"12 de outubro de 2023\" para 1697059200). Essas funcionalidades são perfeitas para desenvolvedores trabalhando em interfaces de usuário, depurando logs ou integrando APIs com diferentes formatos de tempo.", "The 2038 problem affects older 32-bit systems, where timestamps may overflow after January 19, 2038. Modern 64-bit systems and our Unix timestamp converter handle this seamlessly.": "O problema de 2038 afeta sistemas de 32 bits mais antigos, onde timestamps podem transbordar após 19 de janeiro de 2038. Sistemas modernos de 64 bits e nosso conversor de timestamp Unix lidam com isso perfeitamente.", "Backend Developer": "<PERSON><PERSON><PERSON><PERSON>", "This Unix timestamp converter is a lifesaver for debugging server logs. I can convert timestamp to date in seconds or milliseconds with accurate time zone support, and the copy feature is so convenient!": "Este conversor de timestamp Unix é uma salvação para depurar logs do servidor. Posso converter timestamp para data em segundos ou milissegundos com suporte preciso de fuso horário, e a função de copiar é tão conveniente!", "Data Analyst": "Analista de Dados", "As a data analyst, I use this tool to convert date to timestamp for database queries. The ability to pause the current timestamp and choose formats like YYYY-MM-DD hh:mm:ss is fantastic!": "Como analista de dados, uso esta ferramenta para converter data para timestamp para consultas de banco de dados. A capacidade de pausar o timestamp atual e escolher formatos como YYYY-MM-DD hh:mm:ss é fantástica!", "Frontend Developer": "<PERSON>en<PERSON><PERSON>", "The best free Unix timestamp converter I've found! Converting timestamp to date across different time zones for my app's user interface is fast and reliable.": "O melhor conversor gratuito de timestamp Unix que encontrei! Converter timestamp para data através de diferentes fusos horários para a interface do usuário da minha aplicação é rápido e confiável.", "API Developer": "Desenvolvedor de <PERSON>", "This tool simplifies my workflow when I need to convert timestamp to date for API integrations. The multiple format options and time zone support are perfect for my projects.": "Esta ferramenta simplifica meu fluxo de trabalho quando preciso converter timestamp para data para integrações de API. As múltiplas opções de formato e suporte de fuso horário são perfeitas para meus projetos.", "System Administrator": "Administrador de Sistema", "I rely on this Unix timestamp converter to convert date to timestamp for scheduling tasks. The intuitive interface and one-click copy feature make my job so much easier.": "Confio neste conversor de timestamp Unix para converter data para timestamp para agendar tarefas. A interface intuitiva e a função de cópia com um clique tornam meu trabalho muito mais fácil.", "Business Intelligence Analyst": "Analista de Business Intelligence", "For generating reports, this tool's timestamp to date conversion is a must-have. Switching between units and copying timestamps or dates in my preferred format is seamless!": "Para gerar relatórios, a conversão de timestamp para data desta ferramenta é indispensável. Alternar entre unidades e copiar timestamps ou datas no meu formato preferido é perfeito!", "User Reviews of Our Unix Timestamp Converter": "Avaliações de Usuários do Nosso Conversor de Timestamp Unix"}